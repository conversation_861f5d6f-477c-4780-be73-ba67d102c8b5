package au.com.allianceautomation.iython.builtins;

import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import au.com.allianceautomation.iython.PythonExecutionException;
import au.com.allianceautomation.iython.PythonExecutor;

/**
 * Unit tests for the __builtins__ module functionality.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
class BuiltinsModuleTest {
    
    @Test
    @DisplayName("Test __builtins__ variable exists")
    void testBuiltinsVariableExists() throws PythonExecutionException {
        PythonExecutor executor = new PythonExecutor();
        
        // Test that __builtins__ exists and is accessible
        String result = executor.executeCode("print(__builtins__)");
        assertTrue(result.contains("builtins"));
        
        executor.close();
    }
    
    @Test
    @DisplayName("Test dir(__builtins__) returns builtin function names")
    void testDirBuiltins() throws PythonExecutionException {
        PythonExecutor executor = new PythonExecutor();
        
        // Test that dir(__builtins__) returns a list of builtin functions
        String result = executor.executeCode("print(dir(__builtins__))");
        
        // Should contain common builtin function names
        assertTrue(result.contains("print"));
        assertTrue(result.contains("str"));
        assertTrue(result.contains("int"));
        assertTrue(result.contains("float"));
        assertTrue(result.contains("len"));
        assertTrue(result.contains("abs"));
        assertTrue(result.contains("min"));
        assertTrue(result.contains("max"));
        assertTrue(result.contains("sum"));
        assertTrue(result.contains("range"));
        
        executor.close();
    }
    
    @Test
    @DisplayName("Test dir(__builtins__) returns sorted list")
    void testDirBuiltinsSorted() throws PythonExecutionException {
        PythonExecutor executor = new PythonExecutor();
        
        // Test that dir(__builtins__) returns a sorted list
        String result = executor.executeCode("builtins_list = dir(__builtins__)\nprint(builtins_list == sorted(builtins_list))");
        assertTrue(result.contains("True"));
        
        executor.close();
    }
    
    @Test
    @DisplayName("Test len(dir(__builtins__)) returns reasonable count")
    void testBuiltinsCount() throws PythonExecutionException {
        PythonExecutor executor = new PythonExecutor();
        
        // Test that we have a reasonable number of builtin functions
        String result = executor.executeCode("print(len(dir(__builtins__)))");
        
        // Should have at least 30 builtin functions
        String countStr = result.trim();
        int count = Integer.parseInt(countStr);
        assertTrue(count >= 30, "Expected at least 30 builtin functions, got " + count);
        
        executor.close();
    }
    
    @Test
    @DisplayName("Test __builtins__ contains specific functions")
    void testBuiltinsContainsSpecificFunctions() throws PythonExecutionException {
        PythonExecutor executor = new PythonExecutor();

        // Test that specific functions are in __builtins__ by checking the string output
        String result = executor.executeCode("print(dir(__builtins__))");

        // Check that essential functions are present in the output
        assertTrue(result.contains("'print'"));
        assertTrue(result.contains("'len'"));
        assertTrue(result.contains("'type'"));
        assertTrue(result.contains("'str'"));
        assertTrue(result.contains("'int'"));

        executor.close();
    }
    
    @Test
    @DisplayName("Test __builtins__ module type")
    void testBuiltinsModuleType() throws PythonExecutionException {
        PythonExecutor executor = new PythonExecutor();
        
        // Test that __builtins__ has the correct type representation
        String result = executor.executeCode("print(type(__builtins__))");
        assertTrue(result.contains("BuiltinsModule") || result.contains("builtins"));
        
        executor.close();
    }
    
    @Test
    @DisplayName("Test comparison with CPython builtin list")
    void testComparisonWithCPython() throws PythonExecutionException {
        PythonExecutor executor = new PythonExecutor();
        
        // Get our builtin list
        String result = executor.executeCode("print(dir(__builtins__))");
        
        // Check that we have most of the essential Python builtins
        String[] essentialBuiltins = {
            "abs", "bool", "chr", "dict", "dir", "divmod", "enumerate", 
            "float", "hex", "int", "len", "list", "max", "min", "oct", 
            "ord", "pow", "print", "range", "repr", "reversed", "round", 
            "set", "sorted", "str", "sum", "tuple", "type", "zip"
        };
        
        for (String builtin : essentialBuiltins) {
            assertTrue(result.contains("'" + builtin + "'") || result.contains(builtin), 
                      "Missing essential builtin: " + builtin);
        }
        
        executor.close();
    }
}
