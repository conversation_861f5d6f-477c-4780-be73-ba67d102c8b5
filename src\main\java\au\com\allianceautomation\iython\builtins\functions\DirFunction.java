package au.com.allianceautomation.iython.builtins.functions;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import au.com.allianceautomation.iython.builtins.BuiltinRegistry;

/**
 * Python dir() builtin function.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class DirFunction extends AbstractBuiltinFunction {
    
    public DirFunction() {
        super("dir", 0, 1, "dir([object]) -> list of strings");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        if (args.isEmpty()) {
            // Return builtin function names when no argument is provided
            Set<String> builtinNames = BuiltinRegistry.getBuiltinNames();
            List<String> sortedNames = new ArrayList<>(builtinNames);
            Collections.sort(sortedNames);
            return new ArrayList<>(sortedNames);
        }
        
        Object obj = args.get(0);

        // Special handling for BuiltinsModule
        if (obj instanceof au.com.allianceautomation.iython.builtins.BuiltinsModule) {
            au.com.allianceautomation.iython.builtins.BuiltinsModule builtinsModule =
                (au.com.allianceautomation.iython.builtins.BuiltinsModule) obj;
            return new ArrayList<>(builtinsModule.getBuiltinNames());
        }

        Set<String> attributes = new TreeSet<>(); // TreeSet for sorted order

        if (obj == null) {
            // NoneType has no attributes
            return new ArrayList<>(attributes);
        }
        
        Class<?> objClass = obj.getClass();
        
        // Get public fields
        Field[] fields = objClass.getFields();
        for (Field field : fields) {
            attributes.add(field.getName());
        }
        
        // Get public methods
        Method[] methods = objClass.getMethods();
        for (Method method : methods) {
            String methodName = method.getName();
            // Skip common Object methods that aren't typically shown in Python dir()
            if (!methodName.equals("getClass") && !methodName.equals("hashCode") && 
                !methodName.equals("equals") && !methodName.equals("toString") &&
                !methodName.equals("notify") && !methodName.equals("notifyAll") &&
                !methodName.equals("wait")) {
                attributes.add(methodName);
            }
        }
        
        // Add Python-specific attributes for built-in types
        if (obj instanceof String) {
            addStringAttributes(attributes);
        } else if (obj instanceof List) {
            addListAttributes(attributes);
        } else if (obj instanceof java.util.Map) {
            addDictAttributes(attributes);
        }
        
        return new ArrayList<>(attributes);
    }
    
    private void addStringAttributes(Set<String> attributes) {
        String[] stringMethods = {
            "upper", "lower", "strip", "lstrip", "rstrip", "split", "rsplit",
            "replace", "startswith", "endswith", "find", "rfind", "index", "rindex",
            "count", "join", "capitalize", "title", "swapcase", "center", "ljust", "rjust",
            "zfill", "encode", "decode", "isalpha", "isdigit", "isalnum", "isspace",
            "islower", "isupper", "istitle", "isascii", "isprintable", "isdecimal", "isnumeric"
        };
        Collections.addAll(attributes, stringMethods);
    }
    
    private void addListAttributes(Set<String> attributes) {
        String[] listMethods = {
            "append", "extend", "insert", "remove", "pop", "clear", "index", "count",
            "sort", "reverse", "copy"
        };
        Collections.addAll(attributes, listMethods);
    }
    
    private void addDictAttributes(Set<String> attributes) {
        String[] dictMethods = {
            "keys", "values", "items", "get", "pop", "popitem", "clear", "update",
            "setdefault", "copy", "fromkeys"
        };
        Collections.addAll(attributes, dictMethods);
    }
}
